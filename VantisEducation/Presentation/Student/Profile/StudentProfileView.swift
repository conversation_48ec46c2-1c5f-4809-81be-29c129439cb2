//
//  StudentProfileView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI

struct StudentProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionManager = TransactionManager.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) var presentationMode
    @State private var showEditProfile = false
    @State private var showSettings = false
    @State private var showSecurity = false
    @State private var showSupport = false
    @State private var showAbout = false
    @State private var showLogoutAlert = false

    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 32) {
                    // Header
                    headerSection

                    // Profile Header
                    profileHeaderSection

                    // Menu Items
                    menuSection

                    // App Info
                    appInfoSection

                    // Logout Button
                    logoutSection

                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
            .navigationDestination(isPresented: $showEditProfile) {
                StudentEditProfileView()
                    .environmentObject(authViewModel)
            }
            .navigationDestination(isPresented: $showSettings) {
                StudentSettingsView()
            }
            .navigationDestination(isPresented: $showSecurity) {
                StudentSecuritySettingsView()
            }
            .navigationDestination(isPresented: $showSupport) {
                StudentSupportView()
            }
            .navigationDestination(isPresented: $showAbout) {
                StudentAboutView()
            }
        }
        .alert("Sign Out", isPresented: $showLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task {
                    await authViewModel.logout()
                }
            }
        } message: {
            Text("Are you sure you want to sign out?")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Title - left aligned
            VStack(alignment: .leading, spacing: 4) {
                Text("Hồ sơ")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Quản lý tài khoản của bạn")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 16)
        .background(Color.white)
    }

    // MARK: - Profile Header Section
    private var profileHeaderSection: some View {
        VStack(spacing: 0) {
            // Background gradient
            ZStack {
                // Gradient background
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppConstants.Colors.primary.opacity(0.8),
                        AppConstants.Colors.primaryDeep.opacity(0.9)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .frame(height: 120)

                // Decorative circles
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 80, height: 80)
                    .offset(x: 100, y: -30)

                Circle()
                    .fill(Color.white.opacity(0.05))
                    .frame(width: 120, height: 120)
                    .offset(x: -80, y: 20)
            }
            .cornerRadius(24, corners: [.topLeft, .topRight])

            // Content
            VStack(spacing: 20) {
                // Avatar section
                ZStack {
                    // Avatar background with glow effect
                    Circle()
                        .fill(Color.white)
                        .frame(width: 120, height: 120)
                        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 20, x: 0, y: 10)

                    Button(action: {
                        showEditProfile = true
                    }) {
                        ZStack {
                            if let avatarUrl = authViewModel.currentUser?.avatar {
                                AsyncImage(url: URL(string: avatarUrl)) { image in
                                    image
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                } placeholder: {
                                    ZStack {
                                        LinearGradient(
                                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )

                                        Text(authViewModel.currentUser?.initials ?? "S")
                                            .font(.beVietnamPro(.bold, size: 32))
                                            .foregroundColor(.white)
                                    }
                                }
                                .frame(width: 110, height: 110)
                                .clipShape(Circle())
                            } else {
                                ZStack {
                                    LinearGradient(
                                        gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )

                                    Text(authViewModel.currentUser?.initials ?? "U")
                                        .font(.beVietnamPro(.bold, size: 32))
                                        .foregroundColor(.white)
                                }
                                .frame(width: 110, height: 110)
                                .clipShape(Circle())
                            }

                            // Edit overlay
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    ZStack {
                                        Circle()
                                            .fill(AppConstants.Colors.primary)
                                            .frame(width: 32, height: 32)

                                        Image(systemName: "pencil")
                                            .font(.system(size: 14, weight: .semibold))
                                            .foregroundColor(.white)
                                    }
                                    .offset(x: -8, y: -8)
                                }
                            }
                            .frame(width: 110, height: 110)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .offset(y: -60)

                // User info section
                VStack(spacing: 12) {
                    VStack(spacing: 6) {
                        Text(authViewModel.currentUser?.displayName ?? "User")
                            .font(.beVietnamPro(.bold, size: 24))
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Text(authViewModel.currentUser?.email ?? "")
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }


                }
                .offset(y: -40)
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 24)
            .background(Color.white)
        }
        .background(Color.white)
        .cornerRadius(24)
        .shadow(color: Color.black.opacity(0.08), radius: 20, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(Color.black.opacity(0.05), lineWidth: 1)
        )
    }

    // MARK: - Menu Section
    private var menuSection: some View {
        VStack(spacing: 20) {
            HStack {
                Text("Settings")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            VStack(spacing: 12) {
                ModernMenuRow(
                    icon: "person.circle.fill",
                    title: "Account Settings",
                    subtitle: "Manage your account information",
                    iconColor: AppConstants.Colors.primary,
                    iconBackground: AppConstants.Colors.primary.opacity(0.15)
                ) {
                    showEditProfile = true
                }

                ModernMenuRow(
                    icon: "gearshape.fill",
                    title: "App Settings",
                    subtitle: "Notifications, language, and more",
                    iconColor: AppConstants.Colors.secondary,
                    iconBackground: AppConstants.Colors.secondary.opacity(0.15)
                ) {
                    showSettings = true
                }

                ModernMenuRow(
                    icon: "lock.shield.fill",
                    title: "Security",
                    subtitle: "Password, biometrics, and privacy",
                    iconColor: AppConstants.Colors.success,
                    iconBackground: AppConstants.Colors.success.opacity(0.15)
                ) {
                    showSecurity = true
                }

                ModernMenuRow(
                    icon: "questionmark.circle.fill",
                    title: "Help & Support",
                    subtitle: "FAQ, contact us, and feedback",
                    iconColor: AppConstants.Colors.info,
                    iconBackground: AppConstants.Colors.info.opacity(0.15)
                ) {
                    showSupport = true
                }

                ModernMenuRow(
                    icon: "info.circle.fill",
                    title: "About LinkX",
                    subtitle: "Version, terms, and privacy policy",
                    iconColor: AppConstants.Colors.accent,
                    iconBackground: AppConstants.Colors.accent.opacity(0.15)
                ) {
                    showAbout = true
                }
            }
        }
    }

    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(spacing: 12) {
            // App icon and name
            HStack(spacing: 12) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 48, height: 48)
                    .overlay(
                        Text("LX")
                            .font(.beVietnamPro(.bold, size: 18))
                            .foregroundColor(.white)
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text("LinkX Mobile")
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text("Version 1.0.0 (Build 1)")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }

                Spacer()
            }

            Divider()
                .background(Color.black.opacity(0.1))

            Text("© 2025 LinkX. All rights reserved.")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppConstants.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
    }

    // MARK: - Logout Section
    private var logoutSection: some View {
        Button(action: {
            showLogoutAlert = true
        }) {
            HStack(spacing: 12) {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                    .font(.system(size: 18, weight: .medium))

                Text("Sign Out")
                    .font(.beVietnamPro(.semiBold, size: 16))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [AppConstants.Colors.error, Color.red.opacity(0.8)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(16)
            .shadow(color: AppConstants.Colors.error.opacity(0.4), radius: 12, x: 0, y: 6)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    StudentProfileView()
        .environmentObject(AuthViewModel())
}
