import SwiftUI
import UIKit

struct NotificationsView: View {
    @EnvironmentObject var viewModel: NotificationsViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingActionSheet = false
    @State private var selectedNotification: AppNotification?
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // Custom header
                headerSection

                // Search and filters
                searchAndFiltersSection

                // Notifications list
                notificationsList
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 16)
        }
        .refreshable {
            await viewModel.refreshNotifications()
        }
        .navigationBarHidden(true)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.98, green: 0.98, blue: 1.0),
                    Color(red: 0.95, green: 0.97, blue: 1.0)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        )
        .sheet(isPresented: $viewModel.showingSettings) {
            NotificationSettingsView(viewModel: viewModel)
        }
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text("Clear All Notifications"),
                message: Text("This action cannot be undone."),
                buttons: [
                    .destructive(Text("Clear All")) {
                        viewModel.deleteAllNotifications()
                    },
                    .cancel()
                ]
            )
        }
        .onChange(of: viewModel.selectedCategory) { _ in
            viewModel.filterNotifications()
        }
        .onChange(of: viewModel.selectedType) { _ in
            viewModel.filterNotifications()
        }
        .onChange(of: viewModel.searchText) { _ in
            viewModel.filterNotifications()
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Notifications")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                if viewModel.unreadCount > 0 {
                    Text("\(viewModel.unreadCount) unread messages")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                } else {
                    Text("All caught up!")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }

            Spacer()

            // Menu button
            Menu {
                Button("Mark All as Read") {
                    viewModel.markAllAsRead()
                }

                Button("Settings") {
                    viewModel.showingSettings = true
                }

                Divider()

                Button("Clear All", role: .destructive) {
                    showingActionSheet = true
                }
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "ellipsis.circle")
                        .font(.system(size: 14, weight: .medium))
                    Text("Menu")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.bottom, 8)
    }

    // MARK: - Search and Filters Section
    private var searchAndFiltersSection: some View {
        VStack(spacing: 16) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                TextField("Search notifications...", text: $viewModel.searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(AppConstants.Typography.body)

                if !viewModel.searchText.isEmpty {
                    Button("Clear") {
                        viewModel.searchText = ""
                    }
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)

            // Filters
            compactFiltersSection
        }
        .padding(.bottom, 16)
    }

    // MARK: - Compact Filters Section
    private var compactFiltersSection: some View {
        VStack(spacing: 12) {
            // Category filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(NotificationCategory.allCases, id: \.self) { category in
                        CompactFilterChip(
                            title: category.displayName,
                            badge: badgeCountForCategory(category),
                            isSelected: viewModel.selectedCategory == category
                        ) {
                            viewModel.selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, 4)
            }

            // Type filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    CompactFilterChip(
                        title: "All Types",
                        badge: viewModel.notifications.count,
                        isSelected: viewModel.selectedType == nil
                    ) {
                        viewModel.selectedType = nil
                    }

                    ForEach(NotificationType.allCases, id: \.self) { type in
                        CompactFilterChip(
                            title: type.displayName,
                            badge: badgeCountForType(type),
                            isSelected: viewModel.selectedType == type
                        ) {
                            viewModel.selectedType = type
                        }
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
    
    // MARK: - Helper Functions

    private func badgeCountForCategory(_ category: NotificationCategory) -> Int {
        switch category {
        case .all:
            return viewModel.notifications.count
        case .unread:
            return viewModel.unreadCount
        case .today:
            return viewModel.notifications.filter { $0.isToday }.count
        case .thisWeek:
            return viewModel.notifications.filter { $0.isThisWeek }.count
        case .important:
            return viewModel.notifications.filter { $0.priority == .high }.count
        case .academic:
            return viewModel.notifications.filter { $0.category == .academic }.count
        case .general:
            return viewModel.notifications.filter { $0.category == .general }.count
        }
    }

    private func badgeCountForType(_ type: NotificationType) -> Int {
        return viewModel.notifications.filter { $0.type == type }.count
    }
    
    // MARK: - Notifications List
    private var notificationsList: some View {
        Group {
            if viewModel.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text("Loading notifications...")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.top, 60)
            } else if viewModel.filteredNotifications.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "bell.slash")
                        .font(.system(size: 48))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    VStack(spacing: 8) {
                        Text("No Notifications")
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Text(viewModel.searchText.isEmpty ?
                            "You're all caught up!" :
                            "No notifications match your search")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.top, 60)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.filteredNotifications) { notification in
                        NotificationRowView(
                            notification: notification,
                            onTap: {
                                viewModel.handleNotificationAction(notification)
                            },
                            onMarkAsRead: {
                                viewModel.markAsRead(notification)
                            },
                            onDelete: {
                                viewModel.deleteNotification(notification)
                            }
                        )
                    }
                }
                .padding(.bottom, 80) // Extra padding for bottom safe area
            }
        }
    }
}

// MARK: - Compact Filter Chip with Badge
struct CompactFilterChip: View {
    let title: String
    let badge: Int
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(title)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)

                if badge > 0 {
                    Text("\(badge)")
                        .font(AppConstants.Typography.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(isSelected ? AppConstants.Colors.primary : .white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(isSelected ? .white : AppConstants.Colors.primary)
                        )
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(isSelected ? AppConstants.Colors.primary : Color(.systemGray5))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview
#Preview {
    NotificationsView()
        .environmentObject(NotificationsViewModel())
}
